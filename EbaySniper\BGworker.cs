using System;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.Images;
using uBuyFirst.Network;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class Form1
    {
        private void RunningDots()
        {
            switch (barStaticProgress.Caption)
            {
                case "":
                    barStaticProgress.Caption = ".";
                    break;

                case ".":
                    barStaticProgress.Caption = "..";
                    break;

                case "..":
                    barStaticProgress.Caption = "...";
                    break;

                case "...":
                    barStaticProgress.Caption = "";
                    break;

                default:
                    barStaticProgress.Caption = ".";
                    break;
            }
        }

        private void ShowDebugStats()
        {
            if (!txtDebugInfo1.Visible)
                return;

            txtDebugInfo1.Text = $"Find req speed: {Stat.Call_SpeedCounter}/s | Count: {Stat.FindReqCount}\r\n"
                                 + $"Find threads active: {Stat.FindActiveThreads} | Max: {SearchConfigManager.Instance.FindReqMaxThreads}\r\n"
                                 + $"GetItem queue: {RequestQueueManager.Instance.GeneralItemQueueCount} | Count: {Stat.GetItemReqCount}\r\n"
                                 + $"GetItem threads active: {SearchConfigManager.Instance.GetItemDetailsReqMaxThreads - SearchConfigManager.Instance.GetItemQueueLimiter?.CurrentCount} | Max: {SearchConfigManager.Instance.GetItemDetailsReqMaxThreads}\r\n"
                                 + $"Thumbs queue: {ImageTools.ImageQueueAvatars.Count} | Downloaded: {Stat.AvatarReqCount} | Threads: {ImageTools.SemaphoreImage1.CurrentCount}\r\n"
                                 + $"Other images queue: {ImageTools.ImageQueueGallery.Count} | Downloaded: {Stat.OtherImagesReqCount} | Threads: {ImageTools.SemaphoreImage1.CurrentCount}\r\n"
                                 + $"Status Count: {Stat.StatusUpdateCounter} \r\n"
                                 + $"Status: {_mqttManager.GetStatus()} \r\n"
                                 + $"{_ablyClient?.GetAblyClientState()}";
        }

        private bool CheckRemoveFilter(DataRow row, DataList dataList)
        {
            var rowDeleted = false;
            if (!LicenseUtility.CurrentLimits.FiltersEnabled)
                return false;

            var xFilterAlias = XFilterManager.ApplyRemoveRowFilter(row);
            if (!string.IsNullOrEmpty(xFilterAlias))
            {
                rowDeleted = true;
                try
                {
                    var localDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, UserSettings.CurrentTimeZoneInfo);
                    var localDateTimeStr = localDateTime.ToString("s").Replace("T", " ");
                    memoEditFilterLog.Text += $"\r\n{localDateTimeStr}\tRemoved: {row["ItemID"]} Reason: {xFilterAlias}\t\t{row["Title"]} ";
                }
                catch (Exception)
                {
                    //TODO: Handle this
                }

                if (memoEditFilterLog.Text.Length > 1000000 || memoEditFilterLog.Lines.Length > 500)
                    memoEditFilterLog.Text = "";
            }

            Loggers.LogFoundItem(dataList.ItemID, dataList.Title, dataList.StartTimeLocal.Value.DateTime, DateTime.UtcNow, xFilterAlias, dataList.SellerName);

            return rowDeleted;
        }

        private async Task<ItemType> GetSpecificsAsyncRss(string itemID, ApiContext apiContext, SearchSource source)
        {
            Interlocked.Increment(ref Stat.GetItemReqCount);
            await RequestQueueManager.Instance.WaitForGetItemQueueAsync();
            if (source != SearchSource.WAT)
                if (!_searchService.Running)
                {
                    SearchConfigManager.Instance.GetItemQueueLimiter.Release(100);
                    return null;
                }

            ItemType result = null;
            try
            {
                result = await ApiService.GetItemUnsafe(itemID, apiContext, true);
                Stat.GetItemErrors = 0;
            }
            catch (ApiException aex)
            {
                ExM.ubuyExceptionHandler("_(info)_GetItemDetails, " + itemID + " ", aex);

                if (aex.Message == "Inactive application or developer.")
                {
                    EBayAccountsList.Clear();
                    apiContext.ApiCredential.eBayToken = "Token";
                    //TODO:Stop search
                    return null;
                }

                //contains The underlying connection was closed
                if (aex.Message.Contains("Client found response content type")
                    || aex.Message == "This item cannot be accessed because the listing has been deleted, is a Half.com listing, or you are not the seller.")
                    return null;

                foreach (var err in aex.Errors.ToArray())
                {
                    if (err?.ShortMessage?.Contains("oken") == true || err?.LongMessage?.Contains("oken") == true)
                    {
                        EBayAccountsList.Clear();
                        apiContext.ApiCredential.eBayToken = "Token";
                        //TODO:Stop search
                        return null;
                    }
                }

                //The underlying connection was closed: A connection that was expected to be kept alive was closed by the server.
                if (aex.Message == "The operation has timed out"
                    || aex.Message == "HTTP Error Code: 503"
                    || aex.Message == "Unable to connect to the remote server"
                    || aex.Message == "The request was aborted: The operation has timed out.")
                {
                }

                Stat.GetItemErrors++;
                if (Stat.GetItemErrors > 10)
                {
                    Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber + " GetItem switch " + apiContext.SoapApiServerUrl, aex);
                }
            }
            catch (Exception ex)
            {
                Stat.GetItemErrors++;
                if (Stat.GetItemErrors > 10)
                {
                    ExM.ubuyExceptionHandler("_(info)_GetItem switch, " + apiContext.SoapApiServerUrl + itemID + " ", ex);
                }
            }
            finally
            {
                SearchConfigManager.Instance.GetItemQueueLimiter.Release();
            }

            return result;
        }


    }
}
