using System;
using System.Threading;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class CaptchaCooldownManagerTests
    {
        [TestInitialize]
        public void Setup()
        {
            // Clear any existing cooldown before each test
            CaptchaCooldownManager.ClearCooldown();
        }

        [TestMethod]
        public void IsInCooldown_WhenNoCooldownStarted_ReturnsFalse()
        {
            // Act
            var result = CaptchaCooldownManager.IsInCooldown;

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void StartCooldown_SetsIsInCooldownToTrue()
        {
            // Act
            CaptchaCooldownManager.StartCooldown();

            // Assert
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown);
        }

        [TestMethod]
        public void ClearCooldown_SetsIsInCooldownToFalse()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown);

            // Act
            CaptchaCooldownManager.ClearCooldown();

            // Assert
            Assert.IsFalse(CaptchaCooldownManager.IsInCooldown);
        }

        [TestMethod]
        public void RemainingCooldownTime_WhenNoCooldown_ReturnsNull()
        {
            // Act
            var result = CaptchaCooldownManager.RemainingCooldownTime;

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void RemainingCooldownTime_WhenCooldownActive_ReturnsTimeSpan()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();

            // Act
            var result = CaptchaCooldownManager.RemainingCooldownTime;

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Value.TotalMinutes > 4.5); // Should be close to 5 minutes
            Assert.IsTrue(result.Value.TotalMinutes <= 5);
        }

        [TestMethod]
        public void IsCaptchaError_WithSessionIdError_ReturnsTrue()
        {
            // Arrange
            var errorMessage = "Session ID not found in session page HTML.";

            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(errorMessage);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsCaptchaError_WithSessionIdErrorDifferentCase_ReturnsTrue()
        {
            // Arrange
            var errorMessage = "session id not found in session page html.";

            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(errorMessage);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsCaptchaError_WithOtherError_ReturnsFalse()
        {
            // Arrange
            var errorMessage = "Some other error message";

            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(errorMessage);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsCaptchaError_WithNullOrEmpty_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(CaptchaCooldownManager.IsCaptchaError(null));
            Assert.IsFalse(CaptchaCooldownManager.IsCaptchaError(""));
            Assert.IsFalse(CaptchaCooldownManager.IsCaptchaError("   "));
        }

        [TestMethod]
        public void GetStatusMessage_WhenNoCooldown_ReturnsActiveMessage()
        {
            // Act
            var result = CaptchaCooldownManager.GetStatusMessage();

            // Assert
            Assert.AreEqual("Restock auto purchasing is active", result);
        }

        [TestMethod]
        public void GetStatusMessage_WhenInCooldown_ReturnsCooldownMessage()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();

            // Act
            var result = CaptchaCooldownManager.GetStatusMessage();

            // Assert
            Assert.IsTrue(result.Contains("Restock auto purchasing paused due to captcha"));
            Assert.IsTrue(result.Contains("Resuming in"));
        }

        [TestMethod]
        public void IsInCooldown_AfterCooldownExpires_ReturnsFalse()
        {
            // This test would require waiting 5 minutes or mocking time
            // For now, we'll test the logic by manually clearing after a short wait

            // Arrange
            CaptchaCooldownManager.StartCooldown();
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown);

            // Simulate cooldown expiration by clearing it
            Thread.Sleep(100); // Small delay to ensure time passes
            CaptchaCooldownManager.ClearCooldown();

            // Act
            var result = CaptchaCooldownManager.IsInCooldown;

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsLogoutError_WithSigninUrl_ReturnsTrue()
        {
            // Arrange
            var content = "location: https://signin.ebay.com/ws/eBayISAPI.dll?SignIn&ru=https%3A%2F%2Fpay.ebay.com%2Frxo%3Fitem%3D233759070858%26action%3Dcreate%26transactionid%3D-1%26quantity%3D2&sgfl=sm&smuid=edbec3a9-608e-11f0-b425-f7409c03981a";

            // Act
            var result = CaptchaCooldownManager.IsLogoutError(content);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsLogoutError_WithSigninUrlDifferentCase_ReturnsTrue()
        {
            // Arrange
            var content = "Location: https://SIGNIN.EBAY.COM/ws/eBayISAPI.dll?SignIn&ru=test";

            // Act
            var result = CaptchaCooldownManager.IsLogoutError(content);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsLogoutError_WithOtherUrl_ReturnsFalse()
        {
            // Arrange
            var content = "location: https://www.ebay.com/some/other/page";

            // Act
            var result = CaptchaCooldownManager.IsLogoutError(content);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsLogoutError_WithNullOrEmpty_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(CaptchaCooldownManager.IsLogoutError(null));
            Assert.IsFalse(CaptchaCooldownManager.IsLogoutError(""));
            Assert.IsFalse(CaptchaCooldownManager.IsLogoutError("   "));
        }

        [TestMethod]
        public void StartLogoutCooldown_SetsIsInCooldownToTrue()
        {
            // Act
            CaptchaCooldownManager.StartLogoutCooldown();

            // Assert
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown);
        }

        [TestMethod]
        public void GetCooldownType_WhenNoCooldown_ReturnsNone()
        {
            // Act
            var result = CaptchaCooldownManager.GetCooldownType();

            // Assert
            Assert.AreEqual(CooldownType.None, result);
        }

        [TestMethod]
        public void GetCooldownType_WhenCaptchaCooldown_ReturnsCaptcha()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();

            // Act
            var result = CaptchaCooldownManager.GetCooldownType();

            // Assert
            Assert.AreEqual(CooldownType.Captcha, result);
        }

        [TestMethod]
        public void GetCooldownType_WhenLogoutCooldown_ReturnsLogout()
        {
            // Arrange
            CaptchaCooldownManager.StartLogoutCooldown();

            // Act
            var result = CaptchaCooldownManager.GetCooldownType();

            // Assert
            Assert.AreEqual(CooldownType.Logout, result);
        }

        [TestMethod]
        public void GetStatusMessage_WhenLogoutCooldown_ReturnsLogoutMessage()
        {
            // Arrange
            CaptchaCooldownManager.StartLogoutCooldown();

            // Act
            var result = CaptchaCooldownManager.GetStatusMessage();

            // Assert
            Assert.IsTrue(result.Contains("Restock auto purchasing paused due to logout"));
            Assert.IsTrue(result.Contains("Resuming in"));
        }
    }
}
