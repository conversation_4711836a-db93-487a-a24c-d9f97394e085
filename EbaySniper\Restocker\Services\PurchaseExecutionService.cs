using System;
using System.Threading;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Purchasing;
using uBuyFirst.GUI;
using uBuyFirst.Pricing;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service responsible for executing purchase attempts through the Restocker system.
    ///
    /// CRITICAL BUSINESS RULE: This service enforces over-purchase prevention by ensuring that
    /// PurchasedQuantity never exceeds RequiredQuantity for any JobId. This prevents budget
    /// overruns and ensures precise quantity control in the Restocker module.
    ///
    /// The service automatically stops purchasing when the required quantity is reached and
    /// calculates exact quantities needed to prevent over-purchasing.
    ///
    /// CONCURRENCY CONTROL: Uses a single SemaphoreSlim to ensure only one restock action
    /// executes at a time across all jobs. This prevents race conditions and ensures
    /// thread-safe quantity checking and purchasing.
    /// </summary>
    public class PurchaseExecutionService : IDisposable
    {
        private bool _disposed = false;
        private readonly IDailySpendService _dailySpendService;

        /// <summary>
        /// Single semaphore to control concurrent restock actions - only one purchase at a time
        /// </summary>
        private static readonly SemaphoreSlim _restockSemaphore = new(1, 1);

        /// <summary>
        /// Timestamp of the last successful purchase completion, used for adaptive delay calculation
        /// </summary>
        private static DateTimeOffset? _lastSuccessfulPurchaseTime;

        public PurchaseExecutionService(IDailySpendService dailySpendService = null)
        {
            _dailySpendService = dailySpendService ?? new DailySpendService();
        }

        /// <summary>
        /// Attempts to purchase an item for a specific keyword's restock requirements.
        /// Uses semaphore-based concurrency control to prevent race conditions where multiple
        /// concurrent purchase attempts could bypass quantity limits.
        /// </summary>
        /// <param name="dataList">The eBay item to potentially purchase</param>
        /// <param name="keyword">The keyword with restock requirements</param>
        /// <param name="filterAlias">The filter alias that triggered this purchase attempt</param>
        /// <returns>Result of the purchase attempt</returns>
        public async Task<PurchaseExecutionResult> TryPurchaseItemAsync(DataList dataList, Keyword2Find keyword, string filterAlias)
        {
            if (dataList == null)
                throw new ArgumentNullException(nameof(dataList));
            if (keyword == null)
                throw new ArgumentNullException(nameof(keyword));

            // Check if keyword has restock configuration before acquiring semaphore
            if (string.IsNullOrEmpty(keyword.JobId) || keyword.RequiredQuantity <= 0)
            {
                return PurchaseExecutionResult.CreateSkipped("Keyword has no restock configuration");
            }

            // Check if restock operations are in captcha cooldown
            if (CaptchaCooldownManager.IsInCooldown)
            {
                var statusMessage = CaptchaCooldownManager.GetStatusMessage();
                return PurchaseExecutionResult.CreateSkipped($"Captcha cooldown active: {statusMessage}");
            }

            try
            {
                // Acquire the single restock semaphore with timeout to prevent indefinite blocking
                var acquired = await _restockSemaphore.WaitAsync(TimeSpan.FromSeconds(600));
                if (!acquired)
                {
                    return PurchaseExecutionResult.CreateFailure("Timeout waiting for restock lock - another restock action may be in progress", null);
                }

                try
                {
                    // RE-CHECK CAPTCHA COOLDOWN: Check again after acquiring semaphore
                    // This prevents items that passed the initial check from proceeding if cooldown started while waiting
                    if (CaptchaCooldownManager.IsInCooldown)
                    {
                        var statusMessage = CaptchaCooldownManager.GetStatusMessage();
                        return PurchaseExecutionResult.CreateSkipped($"Captcha cooldown active after semaphore acquisition: {statusMessage}");
                    }

                    // OVER-PURCHASE PREVENTION: Re-check quantity after acquiring semaphore
                    // This prevents race conditions where multiple threads pass the initial check
                    if (keyword.PurchasedQuantity >= keyword.RequiredQuantity)
                    {
                        return PurchaseExecutionResult.CreateSkipped("Required quantity already reached");
                    }

                    // Process the purchase for this keyword
                    var result = await ProcessKeywordPurchaseAsync(dataList, keyword, filterAlias);
                    return result;
                }
                finally
                {
                    // Always release the semaphore
                    _restockSemaphore.Release();
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error during purchase execution for item {dataList.ItemID}: {ex.Message}";
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }

        /// <summary>
        /// Processes a purchase attempt for a specific keyword
        /// </summary>
        private async Task<PurchaseExecutionResult> ProcessKeywordPurchaseAsync(
            DataList dataList, Keyword2Find keyword, string filterAlias)
        {
            try
            {
                // Check if credit card checkout is available
                if (!CreditCardService.CreditCardPaymentEnabled)
                {
                    var message = "Credit card payment is not enabled";
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // Validate item availability
                if (dataList.QuantityAvailable <= 0)
                {
                    var message = "Item is not available for purchase";
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // OVER-PURCHASE PREVENTION: Calculate exact quantity to purchase
                // This ensures we never purchase more than required (PurchasedQuantity <= RequiredQuantity)
                var remainingQuantity = keyword.RequiredQuantity - keyword.PurchasedQuantity;
                var quantityToPurchase = Math.Min(remainingQuantity, dataList.QuantityAvailable);

                // Double-check: If no quantity needed, skip the purchase
                if (quantityToPurchase <= 0)
                {
                    var message = "Purchase requirement already fulfilled";
                    return PurchaseExecutionResult.CreateSkipped(message);
                }

                // DAILY SPEND LIMIT CHECK: Calculate purchase amount and check against daily limit
                var purchaseAmountUsd = CalculatePurchaseAmountUsd(dataList, quantityToPurchase);
                if (!_dailySpendService.CanPurchase(purchaseAmountUsd))
                {
                    var todaySpent = _dailySpendService.GetTodaySpent();
                    var dailyLimit = _dailySpendService.GetDailyLimit();
                    var message = $"Daily spend limit exceeded. Today spent: ${todaySpent:F2}, Limit: ${dailyLimit:F2}, Attempted: ${purchaseAmountUsd:F2}";
                    return PurchaseExecutionResult.CreateDailyLimitExceeded(message);
                }

                // Execute the purchase
                var purchaseResult = await ExecutePurchaseAsync(dataList, quantityToPurchase, keyword, filterAlias, purchaseAmountUsd);

                return purchaseResult;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing purchase for keyword {keyword.Id}: {ex.Message}";
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }

        /// <summary>
        /// Executes the actual purchase using the existing CreditCardCheckout system
        /// </summary>
        private async Task<PurchaseExecutionResult> ExecutePurchaseAsync(DataList dataList, int quantity, Keyword2Find keyword, string filterAlias, decimal purchaseAmountUsd)
        {
            try
            {
                // Execute the credit card checkout with the calculated quantity, marking it as a restock purchase
                await CreditCardCheckout.ExecuteCreditCardCheckout(dataList, quantity, isRestockPurchase: true);

                // Monitor the purchase status

                if (dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess
                    || dataList.Order?.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
                {
                    // Update keyword's purchased quantity
                    keyword.PurchasedQuantity += quantity;

                    // Record the purchase amount in daily spend tracking
                    _dailySpendService.RecordPurchase(purchaseAmountUsd);

                    // Save settings to persist the updated purchase quantity
                    // Use thread-safe invocation to avoid cross-thread operation errors
                    await SaveSettingsThreadSafeAsync();

                    // Adaptive delay to ensure at least 2 seconds between restock purchases
                    // If 2+ seconds have already elapsed since last purchase, proceed immediately
                    var now = DateTimeOffset.UtcNow;
                    var minimumIntervalMs = 2000;

                    if (_lastSuccessfulPurchaseTime.HasValue)
                    {
                        var elapsedMs = (now - _lastSuccessfulPurchaseTime.Value).TotalMilliseconds;
                        var remainingDelayMs = minimumIntervalMs - elapsedMs;

                        if (remainingDelayMs > 0)
                        {
                            await Task.Delay((int)Math.Ceiling(remainingDelayMs));
                        }
                    }

                    // Update timestamp to mark completion of this purchase
                    _lastSuccessfulPurchaseTime = DateTimeOffset.UtcNow;

                    return PurchaseExecutionResult.CreateSuccess(
                        $"Successfully purchased {quantity} of item {dataList.ItemID} for job {keyword.JobId} (${purchaseAmountUsd:F2})",
                        quantity);
                }
                else
                {
                    var errorMessage = dataList.Order?.FailureReasonMessage ?? "Purchase failed for unknown reason";

                    // Check if this is a captcha-related error
                    if (CaptchaCooldownManager.IsCaptchaError(errorMessage))
                    {
                        await HandleCaptchaDetectionAsync(dataList, errorMessage);
                        return PurchaseExecutionResult.CreateFailure($"Captcha detected: {errorMessage}. Auto purchasing paused for 5 minutes.");
                    }

                    // Check if this is a logout-related error by examining the session HTML
                    var sessionHtml = dataList.Order?.SessionHtml ?? "";
                    if (CaptchaCooldownManager.IsLogoutError(sessionHtml))
                    {
                        await HandleLogoutDetectionAsync(dataList, "User logged out - signin redirect detected");
                        return PurchaseExecutionResult.CreateFailure("Logout detected: User logged out. Auto purchasing paused for 5 minutes.");
                    }

                    return PurchaseExecutionResult.CreateFailure(errorMessage);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error executing purchase: {ex.Message}";
                return PurchaseExecutionResult.CreateFailure(errorMessage, ex);
            }
        }

        /// <summary>
        /// Calculates the total purchase amount in USD including item price and quantity
        /// </summary>
        private decimal CalculatePurchaseAmountUsd(DataList dataList, int quantity)
        {
            try
            {
                var effectivePrice = dataList.ItemPricing.GetEffectivePurchasePrice();
                if (effectivePrice == null)
                {
                    Form1.Log?.Error("Unable to get effective purchase price for item {0}", dataList.ItemID);
                    return 0m;
                }

                // Calculate total amount (price * quantity)
                var totalAmount = effectivePrice.Value * quantity;

                // Convert to USD
                var totalAmountUsd = CurrencyConverter.ConvertToUSD(totalAmount, effectivePrice.Currency);

                System.Diagnostics.Debug.WriteLine("Purchase amount calculation - Item: {0}, Price: {1} {2}, Quantity: {3}, Total USD: ${4:F2}",
                    dataList.ItemID, effectivePrice.Value, effectivePrice.Currency, quantity, totalAmountUsd);

                return (decimal)totalAmountUsd;
            }
            catch (Exception ex)
            {
                Form1.Log?.Error(ex, "Error calculating purchase amount for item {0}", dataList.ItemID);
                return 0m;
            }
        }

        /// <summary>
        /// Handles captcha detection by opening browser and showing tray alert
        /// </summary>
        private async Task HandleCaptchaDetectionAsync(DataList dataList, string errorMessage)
        {
            try
            {
                // Start the 5-minute cooldown period
                CaptchaCooldownManager.StartCooldown();

                // Open browser with captcha URL - use a generic eBay captcha URL since we don't have the specific one
                var captchaUrl = "https://www.ebay.com/splashui/captcha";
                Browser.LaunchBrowser(null, captchaUrl);

                // Show tray alert to user
                await ShowCaptchaTrayAlertAsync();

                System.Diagnostics.Debug.WriteLine($"Captcha detected for item {dataList.ItemID}. Browser opened and 5-minute cooldown started.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling captcha detection: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows a tray alert notification about captcha detection and cooldown
        /// </summary>
        private async Task ShowCaptchaTrayAlertAsync()
        {
            try
            {
                var form1 = Form1.Instance;
                if (form1 == null) return;

                // Check if we need to invoke on the UI thread
                if (form1.InvokeRequired)
                {
                    // Use TaskCompletionSource to make the synchronous Invoke call awaitable
                    var tcs = new TaskCompletionSource<bool>();

                    form1.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            form1.ShowCaptchaTrayAlert();
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));

                    // Wait for the UI thread operation to complete
                    await tcs.Task;
                }
                else
                {
                    // Already on UI thread, call directly
                    form1.ShowCaptchaTrayAlert();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing captcha tray alert: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles logout detection by opening browser and showing tray alert
        /// </summary>
        private async Task HandleLogoutDetectionAsync(DataList dataList, string errorMessage)
        {
            try
            {
                // Start the 5-minute logout cooldown period
                CaptchaCooldownManager.StartLogoutCooldown();

                // Open browser with eBay login URL
                var loginUrl = "https://signin.ebay.com/ws/eBayISAPI.dll?SignIn";
                Browser.LaunchBrowser(null, loginUrl);

                // Show tray alert to user
                await ShowLogoutTrayAlertAsync();

                System.Diagnostics.Debug.WriteLine($"Logout detected for item {dataList.ItemID}. Browser opened and 5-minute cooldown started.");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling logout detection: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows a tray alert notification about logout detection and cooldown
        /// </summary>
        private async Task ShowLogoutTrayAlertAsync()
        {
            try
            {
                var form1 = Form1.Instance;
                if (form1 == null) return;

                // Check if we need to invoke on the UI thread
                if (form1.InvokeRequired)
                {
                    // Use TaskCompletionSource to make the synchronous Invoke call awaitable
                    var tcs = new TaskCompletionSource<bool>();

                    form1.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            form1.ShowLogoutTrayAlert();
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));

                    // Wait for the UI thread operation to complete
                    await tcs.Task;
                }
                else
                {
                    // Already on UI thread, call directly
                    form1.ShowLogoutTrayAlert();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing logout tray alert: {ex.Message}");
            }
        }

        /// <summary>
        /// Saves settings in a thread-safe manner to avoid cross-thread operation errors
        /// </summary>
        private async Task SaveSettingsThreadSafeAsync()
        {
            try
            {
                var form1 = Form1.Instance;
                if (form1 == null) return;

                // Check if we need to invoke on the UI thread
                if (form1.InvokeRequired)
                {
                    // Use TaskCompletionSource to make the synchronous Invoke call awaitable
                    var tcs = new TaskCompletionSource<bool>();

                    form1.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            form1.SaveSettings();
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));

                    // Wait for the UI thread operation to complete
                    await tcs.Task;
                }
                else
                {
                    // Already on UI thread, call directly
                    form1.SaveSettings();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't let it crash the purchase process
                System.Diagnostics.Debug.WriteLine($"Error saving settings after purchase: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }

        /// <summary>
        /// Static cleanup method to dispose of the restock semaphore
        /// Call this when shutting down the application
        /// </summary>
        public static void DisposeStaticResources()
        {
            try
            {
                _restockSemaphore?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing static resources: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Result of a purchase execution attempt
    /// </summary>
    public class PurchaseExecutionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public int QuantityPurchased { get; set; }
        public bool IsDailyLimitExceeded { get; set; }

        public static PurchaseExecutionResult CreateSuccess(string message, int quantityPurchased = 0)
        {
            return new PurchaseExecutionResult
            {
                Success = true,
                Message = message,
                QuantityPurchased = quantityPurchased
            };
        }

        public static PurchaseExecutionResult CreateFailure(string message, Exception exception = null)
        {
            return new PurchaseExecutionResult
            {
                Success = false,
                Message = message,
                Exception = exception
            };
        }

        public static PurchaseExecutionResult CreateSkipped(string message)
        {
            return new PurchaseExecutionResult
            {
                Success = false,
                Message = message
            };
        }

        public static PurchaseExecutionResult CreateDailyLimitExceeded(string message)
        {
            return new PurchaseExecutionResult
            {
                Success = false,
                Message = message,
                IsDailyLimitExceeded = true
            };
        }
    }
}
