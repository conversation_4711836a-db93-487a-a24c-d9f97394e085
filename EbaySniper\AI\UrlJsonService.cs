using System;
using System.Collections.Generic;
using System.Data;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using uBuyFirst.ExternalData;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.AI
{
    public class UrlJsonService
    {
        private readonly HttpClient _httpClient = new()
        {
            Timeout = TimeSpan.FromSeconds(300)
        };

        /// <summary>
        /// Fetches JSON response from a URL template using the same pattern as SKU manager
        /// Supports both GET and POST requests based on UserSettings.SendDescriptionAndPictures
        /// </summary>
        /// <param name="row">DataRow containing the item data</param>
        /// <returns>JSON response string</returns>
        public async Task<string> FetchJsonFromUrl(DataRow row)
        {
            try
            {
                // Use the ExternalEndpointUrl when in AI mode, similar to how External mode works
                var urlTemplate = UserSettings.ExternalEndpointUrl;

                if (string.IsNullOrEmpty(urlTemplate))
                {
                    return "{\"error\": \"No URL template configured for AI endpoint\"}";
                }

                // Build URL with parameters using existing ExternalDataManager logic
                var finalUrl = ExternalDataManager.BuildExternalDataUrl(urlTemplate, row);
                finalUrl += "&response_type=json";
                if (string.IsNullOrEmpty(finalUrl))
                {
                    return "{\"error\": \"Failed to build URL from template\"}";
                }

                HttpResponseMessage response;

                // Check if we should send POST request (same logic as FormExternalData)
                if (UserSettings.SendDescriptionAndPictures)
                {
                    response = await SendPostRequest(finalUrl, row);
                }
                else
                {
                    response = await _httpClient.GetAsync(finalUrl);
                }

                response.EnsureSuccessStatusCode();
                var jsonContent = await response.Content.ReadAsStringAsync();

                // Return the JSON content directly
                return jsonContent;
            }
            catch (HttpRequestException ex)
            {
                return $"{{\"error\": \"HTTP request failed: {ex.Message}\"}}";
            }
            catch (TaskCanceledException ex)
            {
                return $"{{\"error\": \"Request timeout: {ex.Message}\"}}";
            }
            catch (Exception ex)
            {
                return $"{{\"error\": \"Error fetching from URL: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// Sends POST request with JSON payload containing description, images, and query parameters
        /// Following the same pattern as CustomRequestHandler in ExternalDataManager
        /// </summary>
        private async Task<HttpResponseMessage> SendPostRequest(string url, DataRow row)
        {
            // Extract query parameters from URL (same logic as CustomRequestHandler)
            var queryParams = GetQueryParameters(url);

            // Get description and image paths (same as ExternalDataManager)
            var description = ExternalDataManager.GetDescription(row);
            var imagePaths = ExternalDataManager.GetImagePaths(row);

            // Build JSON payload (same structure as CustomRequestHandler)
            var payload = new Dictionary<string, object>
            {
                { "description", description },
                { "images", imagePaths }
            };

            // Add all query parameters as top-level fields
            foreach (var param in queryParams)
            {
                payload[param.Key] = param.Value;
            }

            // Serialize with settings to ensure proper escaping (same as CustomRequestHandler)
            var settings = new JsonSerializerSettings
            {
                StringEscapeHandling = StringEscapeHandling.EscapeNonAscii
            };
            var jsonData = JsonConvert.SerializeObject(payload, settings);

            // Remove query parameters from URL (same as CustomRequestHandler)
            var uri = new Uri(url);
            var baseUrl = uri.GetLeftPart(UriPartial.Path);

            // Create POST request with JSON content
            var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
            return await _httpClient.PostAsync(baseUrl, content);
        }

        /// <summary>
        /// Extracts query parameters from URL (same logic as CustomRequestHandler)
        /// </summary>
        private Dictionary<string, string> GetQueryParameters(string url)
        {
            var parameters = new Dictionary<string, string>();
            var uri = new Uri(url);

            if (!string.IsNullOrEmpty(uri.Query))
            {
                // Remove the leading '?' and split by '&'
                var queryParams = uri.Query.TrimStart('?').Split('&');
                foreach (var param in queryParams)
                {
                    if (!string.IsNullOrEmpty(param))
                    {
                        try
                        {
                            var parts = param.Split(new[] { '=' }, 2);
                            // Decode and sanitize the key
                            var key = Uri.UnescapeDataString(parts[0]).Replace(" ", "_");

                            // Decode the value, if it exists
                            var value = parts.Length > 1 ? Uri.UnescapeDataString(parts[1]) : string.Empty;

                            // Only add if we have a valid key
                            if (!string.IsNullOrEmpty(key))
                            {
                                parameters[key] = value;
                            }
                        }
                        catch (UriFormatException)
                        {
                            // Skip malformed parameters
                            continue;
                        }
                    }
                }
            }
            return parameters;
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
